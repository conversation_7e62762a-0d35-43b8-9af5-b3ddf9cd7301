#!/bin/bash

# =============================================================================
# Winter Protocol Production Environment Setup Script
# =============================================================================
# This script sets up the complete production infrastructure for Winter Protocol
# Project: winter-protocol-prod
# Region: asia-southeast1
# =============================================================================

set -e  # Exit on any error

echo "🚀 Starting Winter Protocol Production Setup..."
echo "Project: winter-protocol-prod"
echo "Region: asia-southeast1"
echo ""

# =============================================================================
# 1. PROJECT SETUP
# =============================================================================
echo "📋 Step 1: Project Setup"

# Create new project (if not already created)
echo "Creating project winter-protocol-prod..."
gcloud projects create winter-protocol-prod --name="Winter Protocol Production" || echo "Project already exists"

# Set billing account (replace with your billing account ID)
echo "⚠️  MANUAL STEP: Link billing account to winter-protocol-prod"
echo "Run: gcloud billing projects link winter-protocol-prod --billing-account=YOUR_BILLING_ACCOUNT_ID"

# Switch to production project
gcloud config set project winter-protocol-prod

# Enable required APIs
echo "Enabling required APIs..."
gcloud services enable sqladmin.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable compute.googleapis.com
gcloud services enable servicenetworking.googleapis.com
gcloud services enable secretmanager.googleapis.com

echo "✅ Project setup complete"
echo ""

# =============================================================================
# 2. VPC PEERING SETUP
# =============================================================================
echo "📋 Step 2: VPC Peering Setup"

# Reserve IP range for private services
echo "Creating IP range for private services..."
gcloud compute addresses create google-managed-services-default \
    --global \
    --purpose=VPC_PEERING \
    --prefix-length=20 \
    --network=default \
    --description="IP range for private Google services" || echo "IP range already exists"

# Create private connection for Cloud SQL and other Google services
echo "Creating VPC peering connection..."
gcloud services vpc-peerings connect \
    --service=servicenetworking.googleapis.com \
    --ranges=google-managed-services-default \
    --network=default \
    --project=winter-protocol-prod || echo "VPC peering already exists"

# Verify VPC peering was created
echo "Verifying VPC peering..."
gcloud compute networks peerings list --network=default

echo "✅ VPC peering setup complete"
echo ""

# =============================================================================
# 3. CLOUD SQL SETUP
# =============================================================================
echo "📋 Step 3: Cloud SQL Setup"

# Create Cloud SQL instance for production WITH public IP
echo "Creating Cloud SQL instance winter-cardano-db-prod..."
gcloud sql instances create winter-cardano-db-prod \
    --database-version=POSTGRES_16 \
    --tier=db-f1-micro \
    --edition=ENTERPRISE \
    --region=asia-southeast1 \
    --availability-type=ZONAL \
    --storage-type=SSD \
    --storage-size=20GB \
    --storage-auto-increase \
    --network=projects/winter-protocol-prod/global/networks/default \
    --assign-ip \
    --authorized-networks=0.0.0.0/0 \
    --backup \
    --backup-start-time=03:00 \
    --maintenance-window-day=SUN \
    --maintenance-window-hour=04 \
    --maintenance-release-channel=production \
    --deletion-protection \
    --project=winter-protocol-prod

# Set password for postgres user
echo "Setting postgres user password..."
gcloud sql users set-password postgres \
    --instance=winter-cardano-db-prod \
    --password=wintercardanodbprodpassword123 \
    --project=winter-protocol-prod

# Create the main database
echo "Creating database winter-cardano-db-prod..."
gcloud sql databases create winter-cardano-db-prod \
    --instance=winter-cardano-db-prod \
    --charset=UTF8 \
    --collation=en_US.UTF8 \
    --project=winter-protocol-prod

echo "✅ Cloud SQL setup complete"
echo ""

# =============================================================================
# 4. REDIS SETUP
# =============================================================================
echo "📋 Step 4: Redis Setup"

# Create Redis instance for production
echo "Creating Redis instance winter-cardano-redis..."
gcloud redis instances create winter-cardano-redis \
    --size=1 \
    --region=asia-southeast1 \
    --redis-version=redis_7_0 \
    --network=projects/winter-protocol-prod/global/networks/default \
    --connect-mode=private-service-access \
    --project=winter-protocol-prod

echo "✅ Redis setup complete"
echo ""

# =============================================================================
# 5. ARTIFACT REGISTRY SETUP
# =============================================================================
echo "📋 Step 5: Artifact Registry Setup"

# Create Docker repository for production
echo "Creating Artifact Registry winter-cardano-images..."
gcloud artifacts repositories create winter-cardano-images \
    --repository-format=docker \
    --location=asia-southeast1 \
    --description="Winter Cardano Docker images - Production" \
    --project=winter-protocol-prod

echo "✅ Artifact Registry setup complete"
echo ""

# =============================================================================
# 6. SERVICE ACCOUNT SETUP
# =============================================================================
echo "📋 Step 6: Service Account Setup"

# Create service account for GitHub Actions deployments
echo "Creating GitHub Actions service account..."
gcloud iam service-accounts create github-actions \
    --display-name="GitHub Actions Production Deployment Service Account" \
    --description="Service account used by GitHub Actions for production deployments" \
    --project=winter-protocol-prod

# Set the service account email
SA_EMAIL="<EMAIL>"

echo "Granting permissions to service account..."
# Grant comprehensive permissions
gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/run.admin"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/cloudsql.admin"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/cloudsql.client"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/cloudsql.instanceUser"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/artifactregistry.writer"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/compute.networkUser"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/iam.serviceAccountUser"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/viewer"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding winter-protocol-prod \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/logging.logWriter"

# Create service account key
echo "Creating service account key..."
gcloud iam service-accounts keys create github-actions-winter-protocol-prod-key.json \
    --iam-account=$SA_EMAIL \
    --project=winter-protocol-prod

# =============================================================================
# 7. SECRET MANAGER SETUP
# =============================================================================
echo "📋 Step 7: Secret Manager Setup"

# Create database password secret
echo "Creating database password secret..."
echo -n "wintercardanodbprodpassword123" | gcloud secrets create DB_PASSWORD_WINTER_CARDANO_DB_PROD --data-file=- --project=winter-protocol-prod

# Get project number for service account permissions
PROJECT_NUMBER=$(gcloud projects describe winter-protocol-prod --format="value(projectNumber)")

# Grant secret access to GitHub Actions service account
echo "Granting secret access to service accounts..."
gcloud secrets add-iam-policy-binding DB_PASSWORD_WINTER_CARDANO_DB_PROD \
    --member="serviceAccount:$SA_EMAIL" \
    --role="roles/secretmanager.secretAccessor" \
    --project=winter-protocol-prod

# Grant secret access to default Compute Engine service account (for Cloud Run runtime)
gcloud secrets add-iam-policy-binding DB_PASSWORD_WINTER_CARDANO_DB_PROD \
    --member="serviceAccount:${PROJECT_NUMBER}-<EMAIL>" \
    --role="roles/secretmanager.secretAccessor" \
    --project=winter-protocol-prod

# Grant secret access to Cloud Run service account
gcloud secrets add-iam-policy-binding DB_PASSWORD_WINTER_CARDANO_DB_PROD \
    --member="serviceAccount:service-${PROJECT_NUMBER}@serverless-robot-prod.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor" \
    --project=winter-protocol-prod

echo "✅ Secret Manager setup complete"
echo ""

# =============================================================================
# 8. GET CONFIGURATION VALUES
# =============================================================================
echo "📋 Step 8: Getting Configuration Values"

# Get Redis IP
REDIS_IP=$(gcloud redis instances describe winter-cardano-redis --region=asia-southeast1 --project=winter-protocol-prod --format="value(host)")
echo "Production Redis IP: $REDIS_IP"

# Get Cloud SQL IPs
PUBLIC_IP=$(gcloud sql instances describe winter-cardano-db-prod --project=winter-protocol-prod --format="value(ipAddresses[1].ipAddress)")
echo "Production Public IP: $PUBLIC_IP"

PRIVATE_IP=$(gcloud sql instances describe winter-cardano-db-prod --project=winter-protocol-prod --format="value(ipAddresses[0].ipAddress)")
echo "Production Private IP: $PRIVATE_IP"

# Get connection name
CONNECTION_NAME=$(gcloud sql instances describe winter-cardano-db-prod --project=winter-protocol-prod --format="value(connectionName)")
echo "Production Connection Name: $CONNECTION_NAME"

echo "Production Project Number: $PROJECT_NUMBER"
echo ""

# =============================================================================
# 9. VERIFICATION
# =============================================================================
echo "📋 Step 9: Infrastructure Verification"

echo "=== PRODUCTION INFRASTRUCTURE VERIFICATION ==="
echo "Project: winter-protocol-prod"
echo "Region: asia-southeast1"
echo ""

echo "=== SQL INSTANCES ==="
gcloud sql instances list --project=winter-protocol-prod

echo "=== REDIS INSTANCES ==="
gcloud redis instances list --region=asia-southeast1 --project=winter-protocol-prod

echo "=== ARTIFACT REPOSITORIES ==="
gcloud artifacts repositories list --location=asia-southeast1 --project=winter-protocol-prod

echo "=== VPC PEERING ==="
gcloud compute networks peerings list --network=default --project=winter-protocol-prod

echo "=== SECRETS ==="
gcloud secrets list --project=winter-protocol-prod

echo "=== SERVICE ACCOUNTS ==="
gcloud iam service-accounts list --project=winter-protocol-prod --filter="email:github-actions*"

echo ""
echo "🎉 PRODUCTION SETUP COMPLETE!"
echo ""
echo "📋 SUMMARY:"
echo "✅ Project: winter-protocol-prod"
echo "✅ Region: asia-southeast1"
echo "✅ Cloud SQL: winter-cardano-db-prod (with public IP)"
echo "✅ Redis: winter-cardano-redis"
echo "✅ Artifact Registry: winter-cardano-images"
echo "✅ Service Account: <EMAIL>"
echo "✅ Secret: DB_PASSWORD_WINTER_CARDANO_DB_PROD"
echo ""
echo "📁 Files created:"
echo "  - github-actions-winter-protocol-prod-key.json"
echo ""
echo "🔧 Next steps:"
echo "1. Set up GitHub environment 'winter-protocol-production'"
echo "2. Configure environment variables and secrets"
echo "3. Test deployment"
echo ""
echo "✅ Service account setup complete"
echo "Service Account: $SA_EMAIL"
echo "Key File: github-actions-winter-protocol-prod-key.json"
