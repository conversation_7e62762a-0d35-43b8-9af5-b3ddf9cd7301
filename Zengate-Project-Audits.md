# Zengate Global - Project Services Mapping and Technical Audit

**Document Version:** 1.0
**Last Updated:** January 14, 2025
**Purpose:** Comprehensive mapping of all services, frameworks, hosting infrastructure, and repositories across Zengate Global projects

---

## Executive Summary

This document provides a complete technical audit and service mapping for Zengate Global's project portfolio, covering five major projects: PALM Portal, Winter Protocol, Palmyra Pro (Nature's Nectar), Palmyra Express E-Commerce Platform, and Palmyra Connect B2B Agricultural Marketplace. It details the technology stack, hosting infrastructure, and administrative access points across our ecosystem.

---

## 1. PALM Portal - Cardano/EVM Staking Platform

**Purpose:** Platform dashboard for PALM token staking on Cardano and BSC networks

### Technology Stack
**Frontend:**
- Next.js 15 with React 18 and TypeScript
- TailwindCSS 3 with shadcn/ui components
- Zustand for state management
- RainbowKit + Wagmi for EVM wallet integration
- Mesh SDK for Cardano wallet integration
- Bun as package manager

**Backend:**
- Smart contracts written in Aiken DSL
- Multiple offchain services for emissions, rewards, and staking
- Indexers for Cardano and BSC analytics
- PALM Analytics service for token metrics

**Blockchain Integration:**
- Cardano (primary) and BSC (secondary) networks
- Automated reward distribution every Monday
- ISPO (Initial Stake Pool Offering) program

### Hosting & Deployment
- **Frontend:** Vercel with GitHub Actions CI/CD
- **API Services:** Custom infrastructure
  - cardano-mainnet.palmyra.app
  - evm-mainnet.palmyra.app
  - analytics.palmyra.app
- **Branches:** mainnet (production), testnet (preview), debug (development)

### GitHub Repositories
**Smart Contracts:**
- Cardano: palm-emissions-contract, palm-reward-release, palm-staking-contracts
- BSC: palm-evm-contracts (Emissions.sol, RewardRelease.sol, PalmStaker.sol)

**Offchain Services:**
- emissions-offchain-executor, rr-offchain-executor, staking-offchain-executor
- staking-indexer (Cardano), palm-evm-indexer (BSC)
- palm-economy-analytics-backend

---

## 2. Winter Protocol - Cardano Backend for NFT Minting

**Purpose:** Backend service for submitting metadata to IPFS and minting NFTs with CID references

### Technology Stack
**Backend:**
- NestJS 11 with Node.js LTS and TypeScript
- PostgreSQL database with Redis for queuing
- Blockfrost API and MeshJS for Cardano integration
- Pinata for IPFS pinning service
- Docker containerization

**Development Tools:**
- Jest for testing, ESLint/Prettier for code quality
- Swagger/OpenAPI for documentation
- Bruno for API testing

### Hosting & Deployment
- **Platform:** Google Cloud Platform (GCP)
- **Services:** Cloud Run (serverless containers), Cloud SQL (PostgreSQL), Cloud Memorystore (Redis)
- **Container Registry:** GCP Artifact Registry
- **Configuration:** 1Gi memory, auto-scaling (0-2 instances)

### API Endpoints
- POST /ipfs - Upload metadata to IPFS
- POST /palmyra/tokenizeCommodity - Mint NFT with CID reference
- POST /palmyra/spendCommodity - Burn NFT and remove UTxO
- POST /palmyra/recreateCommodity - Transfer NFT to new UTxO
- GET /check/:id - Check transaction status

### GitHub Repository
- **Main Backend:** https://github.com/zenGate-Global/winter-backend-cardano

---

## 3. Palmyra Pro (Nature's Nectar) - GCP Microservices

**Purpose:** Full-stack application with microservices architecture

### Technology Stack
**Frontend:**
- Next.js 14 with React 18 and TypeScript
- Tailwind CSS 3 with Shadcn UI components
- Serwist for PWA features

**Backend:**
- Go Lang 1.24 for API services
- Node.js 22 with Better Auth for authentication
- PostgreSQL 17.4 database
- Turborepo for monorepo management

### Hosting & Deployment
- **Platform:** Google Cloud Platform (GCP)
- **Services:** 4 microservices on Cloud Run
  - honey-producers (Frontend - Next.js)
  - auth (Authentication - Node.js/Better Auth)
  - api (Backend API - Go Lang)
  - winter-protocol (Blockchain Service - Go Lang)
- **Database:** GCP Cloud SQL (PostgreSQL)
- **Analytics:** Metabase (zengate-global.metabaseapp.com)

### Container Configuration
- **Base Images:** node:22-alpine, golang:1.24-alpine
- **Features:** Multi-stage builds, health checks, internal networking
- **Registry:** GCP Artifact Registry (palmyra-pro-images)

### GitHub Repository
- **Monorepo:** https://github.com/zenGate-Global/palmyra-pro

---

## 4. Palmyra Express - E-Commerce Platform

**Purpose:** Headless e-commerce platform with multi-payment support

### Technology Stack
**Backend:**
- Medusa.js 2.8.4 (headless e-commerce framework)
- Node.js 22.12.0 with TypeScript
- PostgreSQL database with MikroORM
- Redis for caching and message queuing
- SendGrid for email notifications

**Frontend:**
- Next.js 15.3.1 with React 19.0.0-rc
- TypeScript and Tailwind CSS
- Medusa UI components with Framer Motion animations

**Payment Integration:**
- Stripe (traditional card payments)
- Coinbase Commerce (cryptocurrency)
- Cardano Mercury (Zengate's Cardano integration)
- PayPal and Ethereum wallet support (Wagmi)

### Hosting & Deployment
- **Backend:** Railway Cloud Platform (serverless containers)
- **Frontend:** Vercel Edge Network (static site with serverless functions)
- **Database:** Railway PostgreSQL managed service
- **Cache:** Railway Redis managed service
- **File Storage:** Supabase Storage (S3-compatible) with CDN

### Deployment Configuration
- **Auto-deploy:** develop branch → staging
- **Manual deploy:** main branch → production (not yet configured)
- **Containerization:** Platform-dependent (no application-level containers)

### GitHub Repositories
- **Backend:** https://github.com/zenGate-Global/palmyra-express.git
- **Frontend:** https://github.com/zenGate-Global/palmyra-express-storefront.git

---

## 5. Palmyra Connect - B2B Agricultural Marketplace

**Purpose:** B2B agricultural marketplace platform with escrow payment functionality and supply chain traceability

### Technology Stack
**Backend:**
- Medusa.js 1.20.2 (headless e-commerce platform)
- Node.js 17+ with TypeScript 5.3.3
- PostgreSQL database with TypeORM 0.3.16
- Redis for caching and event bus
- SendGrid for email notifications
- Google OAuth for social authentication

**Frontend:**
- Next.js 14.2.4 with React 18 and TypeScript 5.3.2
- Ant Design 4.24.9 component library
- SASS 1.77.6 for styling
- Redux Toolkit 2.2.6 for state management
- Yandex Maps for geographic visualization
- React Intl 6.6.8 for internationalization

**Payment Processing:**
- MangoPay (primary) - escrow functionality with 3D Secure support
- Revolut (alternative payment processor)
- Wire transfer and exchange payment handling

**Specialized Features:**
- Supply chain traceability with geographic visualization
- B2B marketplace functionality
- Escrow payment system for secure transactions
- Multi-language support

### Hosting & Deployment
- **Backend:** Railway Cloud Platform (automated GitHub deployment)
- **Frontend:** Vercel Cloud Platform (automated GitHub deployment)
- **Database:** Railway PostgreSQL (medusa-starter-default)
- **Cache:** Railway Redis (managed service)
- **File Storage:** Supabase Storage with CDN (project cshorchghzzrisrogsgk)

### External Integrations
- **Shipping:** DHL International Express and DHL eCommerce
- **Analytics:** Segment event tracking
- **Authentication:** Google OAuth integration
- **Maps:** Yandex Maps for traceability visualization
- **Email:** SendGrid with custom templates

### GitHub Repositories
- **Backend:** https://github.com/zenGate-Global/palmyra
- **Frontend:** https://github.com/zenGate-Global/palmyra-storefront

### Current Limitations
- **No Monitoring:** No monitoring or observability tools implemented
- **Platform Dependency:** Railway/Vercel managed deployment (no custom containerization)
- **Limited Production Control:** Platform-dependent deployment processes

---

## 6. Administrative Access and Platform Management

### Platform Access Requirements
- **Vercel:** PALM Portal, Palmyra Express, and Palmyra Connect frontend deployments
- **Google Cloud Platform:** Winter Protocol and Palmyra Pro (full project access required)
- **Railway:** Palmyra Express and Palmyra Connect backend, database, and Redis management
- **Supabase:** Palmyra Express and Palmyra Connect file storage and CDN management
- **GitHub:** All projects (admin/maintainer access for repository and secrets management)
- **Metabase:** Palmyra Pro analytics dashboard access

### Secrets Management
- **GitHub Secrets:** CI/CD secrets and service account keys for all projects
- **GCP Secret Manager:** Production secrets for Winter Protocol and Palmyra Pro
- **Railway Dashboard:** Production secrets for Palmyra Express and Palmyra Connect backends
- **Vercel Dashboard:** Environment variables for frontend deployments
- **Supabase Project:** Storage keys and configuration

---

## 7. Monitoring and Current Limitations

### Current Monitoring Status
- **PALM Portal:** Vercel analytics, blockchain monitoring for weekly reward cycles
- **Winter Protocol:** GCP Cloud Monitoring with auto-scaling, basic database monitoring
- **Palmyra Pro:** GCP Cloud Monitoring for all services, Metabase for business intelligence
- **Palmyra Express:** Limited to basic platform monitoring via Railway and Vercel
- **Palmyra Connect:** No monitoring implemented (development/production environments)

### Key Limitations and TODOs
**High Priority:**
- PALM Portal: BSC Emissions and Reward Release offchain services not implemented
- Palmyra Express: Production deployment not configured (manual deployment needed)
- Palmyra Connect: No monitoring or observability tools implemented

**Medium Priority:**
- Winter Protocol: Health checks not configured
- Palmyra Express: Application containerization needed (currently platform-dependent)
- Palmyra Express: Enhanced monitoring beyond platform-level
- Palmyra Connect: Platform-dependent deployment (no custom containerization)

**Low Priority:**
- PALM Portal: Unit tests not in place
- Winter Protocol: Enhanced monitoring setup
- Palmyra Pro: Advanced analytics configuration

---

*This document serves as a comprehensive technical audit and will be updated as new projects are added or existing infrastructure changes.*
