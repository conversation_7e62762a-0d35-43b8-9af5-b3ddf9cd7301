services:
  winter-backend-cardano:
    build: .
    volumes:
      - .:/usr/src/app
    ports:
      - "4000:4000"
    env_file:
      - .env
    depends_on:
      - redis
      - db
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G

  redis:
    image: redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --save 60 1 --loglevel warning

  db:
    image: postgres
    ports:
      - "5432:5432"
    restart: unless-stopped
    env_file: .env
    volumes:
      - postgres-data:/var/lib/postgresql/data
    logging:
      driver: "json-file"
      options:
        max-size: "100M"

volumes:
  redis-data:
  postgres-data: