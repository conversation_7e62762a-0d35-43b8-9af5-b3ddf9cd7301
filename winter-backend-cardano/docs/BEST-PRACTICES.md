# Best Practices
This is a document outlining some best-practices for getting the project setup locally or within a hosted environment.

It is important to note that this is an MVP/PoC meant to be integrated within another application, and not meant to be used as a standalone service. 

## Local Deployment
Local deployment is used for development and testing any changes made. 

- It is recommended to use <PERSON><PERSON> for running the project locally instead of build and running it with npm/yarn/bun. 
- Make sure to provide a valid PORT number as an environment variable for the application to listen to incoming requests.

## Google Cloud Deployment
Within zenGate, we are using Google Cloud as our hosting infrastructure for most of our projects.

- Use Google Cloud Run for deploying the containerized application, make sure to provide enough memory to the instance, otherwise the project will fail to startup. More than 500 MB will be needed.
- Use Google Cloud Memorystore for Redis for hosting the requried Redis instance. For a staging environment, it is fine to use the Basic Tier. However, for a production environment, it is recommended to use the Standard Tier with one replica in case of failover from the primary instance. The queue cannot be flushed otherwise requests will need to be resubmitted to the application for submitting transactions to the Cardano blockchain.
- With Google Cloud Memory Store for Redis, create the instance using the Private Service Access connection mode, ensure you provide the correct subnet value as an environment variable when wanting to connect to the instance.
- Note that unlike the Google Cloud Run, the Redis instance does not get recreated for each new deployment. 
- Use Google Cloud SQL for hosting the required Postgres database instance.
- The repository uses a GitHub workflow to deploy a Google Cloud instance whenever changes are merged/pushed to the remote repository, which is used as our staging or production environment depending on the branch. This is useful for testing integrations of the service within other applications.

## Common Google Cloud Deployment Errors
- The deployment instance does not have enough memory assigned to it.
- The deployment instance does not have correct environment variables assigned to it, so certain services will not be initialized properly or fail to communicate with other services.